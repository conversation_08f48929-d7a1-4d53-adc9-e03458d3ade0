---
description: 
globs: 
alwaysApply: true
---
# 章节拆分规则

## 目标与原则

- **目标**: 将单个大型Markdown习题集文件按章节拆分为多个独立文件
- **输入**: 一个包含多章节内容的Markdown文件
- **输出**: 多个按章节组织的独立Markdown文件，保存在`章节拆分/`目录下
- **内容完整性**: 拆分过程中必须保持原始内容完整，包括文本、公式、图片等
- **文件命名**: 按照章节编号和名称进行规范命名

## 详细操作流程

1. **预处理与目录创建**:
   - 在工作目录下创建`章节拆分/`目录，用于存放拆分后的章节文件
   - 读取原始Markdown文件，分析文件结构和章节标记

2. **章节识别标准**:
   - 识别一级标题作为章节标记，例如`# 第一章 绪论`、`# 第1章 混凝土结构设计原理`
   - 支持的章节标记模式包括但不限于:
     - `# 第N章 [章节名称]`
     - `# 第[汉字数字]章 [章节名称]` 
     - `# [罗马数字]. [章节名称]`
     - `# [章节名称]`

3. **内容分割与提取**:
   - 从一个章节标记开始，到下一个章节标记（或文件末尾）之间的所有内容视为该章节的完整内容
   - 章节内容必须包含该章节的标题行
   - 对于章节前的内容（如前言、引言等），可选择性地保存为单独文件（如`前言.md`）

4. **文件保存规则**:
   - 文件命名格式: `第N章_章节名称.md`或`章节N_章节名称.md`
   - 文件名中应去除特殊字符（如`/`、`\`、`:`等），替换为下划线或其他合法字符
   - 确保文件名唯一性，必要时增加额外标识

5. **元数据处理**:
   - 可在拆分文件开头添加YAML前置元数据，标记章节编号、名称等信息
   - 元数据格式示例:
     ```
     ---
     chapter: 1
     title: 绪论
     ---
     ```

## 特殊情况处理

1. **复杂嵌套结构**:
   - 对于包含多级结构的章节（如1.1、1.2等小节），保持原有结构完整，不做进一步拆分
   
2. **图片与外部资源**:
   - 拆分时保持原有图片引用路径，不修改图片链接
   - 对于可能失效的相对路径，可在后续处理中统一调整

3. **跨章节内容**:
   - 对于不明确属于某章节的内容，根据上下文判断并归入最相关章节
   - 无法判断归属的内容可单独保存为独立文件（如`其他.md`）

4. **空章节处理**:
   - 如识别到没有实际内容的章节，应创建包含章节标题的空文件，以保持章节完整性

## 质量验证

拆分完成后，应执行以下验证:
- 确认所有章节均已提取并保存
- 验证文件内容完整性，确保无遗漏或重复
- 检查特殊格式（如公式、表格）是否正确保留