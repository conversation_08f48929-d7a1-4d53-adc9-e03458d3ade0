---
description: 
globs: 
alwaysApply: false
---
# 题目拆分规则

## 一、 总体原则

- **目标**: 将一个包含多章节、多题型的大型 Markdown 习题集文件，逐步拆分为按章节、按题型、按单个题目组织的独立 Markdown 文件。
- **输入**: 单个 Markdown 文件 (例如 `原始习题集.md`)。
- **中间输出**: 依次生成 `章节拆分`, `题型拆分`, `题目拆分` 三个目录，分别包含按相应层级拆分后的 Markdown 文件。
- **内容完整性与答案包含**: 在所有拆分阶段，**必须** 保持原始 Markdown 文件中的文本内容（包括但不限于文本、**数学公式**、代码块等）**完全不变**，确保无任何转义、格式修改或内容增删。拆分仅是对内容的物理分割和重新组织。进行题型拆分和题目拆分时，**必须** 将题目对应的**答案或解析**完整地包含在同一个拆分后的文件中。
- **目录结构**: 必须严格按照下面各阶段定义的目录结构和文件命名规则进行。
- **处理方式**: 本规则描述的是文件内容的 **物理拆分逻辑**，应通过读取源文件内容并根据标记进行分割来实现，而非生成新内容。

## 二、 拆分阶段详解

### 阶段 1: 章节拆分

- **目标**: 将原始 Markdown 文件按章节拆分为多个独立的 Markdown 文件。
- **输出目录**: `章节拆分/`
- **文件命名**: 章节文件应使用章节序号和名称命名，例如 `第1章_绪论.md`。

### 阶段 2: 题型拆分

- **目标**: 将章节文件进一步按照题型拆分为独立文件。
- **输出目录**: `题型拆分/`
- **目录结构**: 每个章节对应一个子目录，子目录内包含该章节下各题型的文件。
- **操作**:
    1.  为 `章节拆分/` 目录中的 **每个** 章节文件，在 `题型拆分/` 目录下创建一个对应的子目录，子目录名称与章节文件名（去除 `.md` 后缀）相同。
    2.  读取每个章节文件的内容。
    3.  识别章节文件内部的题型标记 (通常是二级或三级标题，例如 `## (一) 填空题`, `### 单项选择题` 或类似的模式)。
    4.  从一个题型标记开始，到下一个题型标记（或章节文件末尾）之间的所有内容（包括题型标题、该题型下的所有题目**及其对应的答案/解析**），视为该题型的完整内容。
    5.  将每个识别出的题型内容保存为一个独立的 Markdown 文件，存放在对应的章节子目录下。
    6.  文件名应能清晰反映题型信息，例如 `填空题.md`, `判断题.md`, `单项选择题.md`。

### 阶段 3: 题目拆分

- **目标**: 将题型文件进一步拆分为单个题目文件，每个文件包含一个题目及其答案/解析。
- **输出目录**: `题目拆分/`
- **目录结构**: 保持章节和题型的两级目录结构，并在题型目录下添加单个题目文件。
- **操作**:
    1.  参照 `题型拆分/` 的目录结构，在 `题目拆分/` 目录下创建对应的 **章节子目录** 和 **题型子目录**。
    2.  遍历 `题型拆分/` 下的每个题型文件。
    3.  识别题型文件内部的单个题目标记 (通常是题目序号，例如 `1.`, `2.`，或者特定的题目开始标记)。
    4.  从一个题目标记开始，到下一个题目标记（或题型文件末尾）之间的内容，视为一个独立的题目**及其必须包含的答案/解析**。
    5.  将每个识别出的独立题目内容保存为一个单独的 Markdown 文件，存放在 `题目拆分/` 下对应的章节和题型子目录中。
    6.  文件名应使用题目的序号，例如 `1.md`, `2.md`。

## 三、 详细的题目拆分规则

1. **目录准备**:
   - 按照原有目录结构创建`题目拆分/章节名称/题型名称/`层级目录
   - 例如：`题目拆分/第1章_绪论/填空题/`

2. **题目识别标准**:
   - 识别以下常见题目标记模式:
     - 数字+点+空格: `1. `, `2. `, `10. `
     - 数字+括号: `(1)`, `（2）`, `[3]`
     - 数字+中文顿号: `1、`, `2、`
     - 题目关键词: `题目1:`, `例题1.1`, `习题2:`
   - 支持阿拉伯数字、中文数字、罗马数字编号
   - 对连续多题（如`1-5.`）进行合理拆分

3. **题目边界判定规则**:
   - 主要依据: 下一个题目的开始标记
   - 辅助判断:
     - 段落间明显间隔
     - 解析、答案等关键词标记
     - 题目内容与答案解析之间的分隔符（如横线、分割线）

4. **题目内容提取**:
   - 题目正文: 从题目标记开始，到答案/解析开始或下一题开始
   - 答案/解析: 识别`答案`、`解析`、`解`、`答`等标记，确保与题目一起提取
   - 图表处理: 确保与题目相关的图片、表格包含在同一文件中

5. **特殊题目类型处理**:
   - **选择题**: 识别选项ABCD与题干的关系，确保完整提取
   - **填空题**: 确保填空符号（如下划线）正确保留
   - **计算题/证明题**: 识别可能跨越多段落的完整解答过程
   - **多小问题目**: 如`(1)(2)(3)`子问题保持在同一文件中

6. **文件命名规则**:
   - 标准命名: 使用题目序号，如`1.md`、`2.md`
   - 复杂情况: 可增加额外信息，如`1_例题.md`、`2_附加题.md`
   - 确保文件名符合操作系统规范，移除非法字符

7. **元数据添加**:
   - 在文件开头添加题目元数据:
     ```
     ---
     id: 1
     chapter: 第1章
     questionType: 填空题
     difficulty: 中等  # 如果原文档中有标记
     ---
     ```

## 四、 注意事项

- 章节、题型、题目的识别标记可能因原始 Markdown 文件格式不同而变化，需要根据实际情况调整识别规则（例如标题级别、特定关键词等）。
- **答案/解析必须跟随**: 答案/解析部分通常紧跟在题目内容之后，拆分时**必须**确保题目与其对应的答案/解析保持在同一个文件中，不可遗漏。
- **公式与内容保真**: 拆分过程中，必须保证原始文本，**特别是 LaTeX 或 MathJax 等格式的数学公式**，不发生任何形式的转义、修改或丢失，保持绝对的原样。
- 对于包含图片或其他媒体资源的 Markdown 文件，拆分时应确保相对路径的引用在拆分后仍然有效，或者考虑在后续处理（如 JSON 转换）中统一处理资源路径。
- 不需要写任何脚本批量处理，就一个一个处理

## 五、 质量控制

拆分完成后，应执行以下验证:
- 所有题目均已正确识别并提取
- 每个题目文件包含完整的题干和答案/解析
- 特殊内容（公式、图表）正确保留
- 确认文件放置在正确的目录结构中
- 抽样检查题目完整性和内容正确性