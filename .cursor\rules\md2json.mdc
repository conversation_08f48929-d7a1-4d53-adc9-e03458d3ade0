---
description: 
globs: 
alwaysApply: true
---
# 题目转JSON规则

## 基本规则

- **目标**: 将单个题目 Markdown 文件转换为结构化的 JSON 文件。
- **输出位置**: JSON 文件应存储在指定的输出目录中（例如 `json/`），并按照原始 Markdown 文件所在的章节和题型子目录结构进行组织。
- **格式标准**: JSON 对象的结构和字段需严格按照本规则定义。
- **内容来源**: JSON 文件中的所有数据必须来源于原始 Markdown 文件或其文件/目录结构。**严禁凭空编造** Markdown 文件中不存在的信息（如难度、知识点、正确率等，除非本规则指定了默认值）。
- **默认值**:
    - `keywords`: `[]` (空数组)
    - `contentMedia`: `[]` (空数组)
    - `analysisMedia`: `[]` (空数组)
    - `relatedQuestions`: `[]` (空数组)
    - `source`: `[]` (空数组)
    - `tags`: `[]` (空数组)
    - `knowledgePoints`: `[]` (空数组)
    - `version`: `"1.0.0"` (字符串)
- **必填字段**: 每个题目 JSON 都必须包含以下字段：
    - `id`: 题目编号 (例如，从文件名或 Markdown 内容中提取的 "1", "2" 等)。
    - `chapter`: 章节信息，一个包含 `id` (例如 "1") 和 `name` (例如 "绪论") 的对象。
    - `chapterHierarchy`: 章节层级，一个包含章节名称的数组 (例如 `["第1章 绪论"]`)。如果原始文档有更详细层级，应包含完整路径。
    - `questionType`: 题型名称 (例如 "填空题", "单项选择题")。
    - `grade`: 题目适用的年级 (如果 Markdown 中没有，则为 `null`)。
    - `year`: 题目来源的年份 (如果 Markdown 中没有，则为 `null`)。
    - `difficulty`: 题目难度 (如果 Markdown 中没有，则为 `null`)。
    - `accuracy`: 题目正确率 (如果 Markdown 中没有，则为 `null`)。
    - `content`: 题目正文内容 (字符串)。
    - `contentMedia`: 题目正文相关的媒体资源（如图片）列表，每个元素包含 `type` 和 `url`。默认为 `[]`。
    - `analysis`: 题目解析内容 (字符串)。如果 Markdown 中没有解析，则为空字符串 `""`。
    - `analysisMedia`: 题目解析相关的媒体资源列表。默认为 `[]`。
    - `keywords`: 关键词列表 (字符串数组)。默认为 `[]`。
    - `relatedQuestions`: 相关题目 ID 列表。默认为 `[]`。
    - `source`: 题目来源信息。默认为 `[]`。
    - `tags`: 标签列表 (字符串数组)。默认为 `[]`。
    - `knowledgePoints`: 关联的知识点列表。默认为 `[]`。
    - `version`: JSON 格式版本号。默认为 `"1.0.0"`。
- **内容处理**:
    - 对于 **选择题**，`content` 字段**不应**包含选项文本 (A, B, C, D...)，选项应在 `options` 字段中表示。
    - 对于 **填空题**，`content` 字段中使用 `_____` (连续五个下划线) 表示需要填写的空格。

## 各题型特殊规则

除了上述基本字段外，不同题型还需要包含以下特定字段：

### 选择题 (单选或多选)

- **额外字段**:
    - `options`: 一个对象数组，每个对象代表一个选项，包含 `key` (例如 "A") 和 `value` (选项的文本内容)。
    - `answer`: 正确答案。
        - 对于单选题，`answer` 是一个字符串，值为正确选项的 `key` (例如 `"A"`)。
        - 对于多选题，`answer` 是一个字符串数组，包含所有正确选项的 `key` (例如 `["A", "C"]`)。
- **示例**:
```json
{
  // ... 其他基本字段 ...
  "questionType": "单项选择题",
  "content": "在其他条件相同时, 钢筋混凝土梁的抗裂能力与素混凝土梁相比 (   )。",
  "options": [
    { "key": "A", "value": "相同" },
    { "key": "B", "value": "提高许多" },
    { "key": "C", "value": "提高不多" },
    { "key": "D", "value": "降低" }
  ],
  "answer": "C",
  "analysis": "解析内容...",
  // ... 其他基本字段 ...
}
```

### 判断题

- **额外字段**:
    - `answer`: 一个布尔值，`true` 表示正确，`false` 表示错误。
- **示例**:
```json
{
  // ... 其他基本字段 ...
  "questionType": "判断题",
  "content": "钢筋与混凝土能够共同工作是因为两者具有相近的力学性能。",
  "answer": false,
  "analysis": "解析内容...",
  // ... 其他基本字段 ...
}
```

### 填空题

- **额外字段**:
    - `blanks`: 一个对象数组，每个对象代表一个填空位置，包含 `position` (从 1 开始的空格序号) 和 `answer` (该空格的参考答案，字符串)。
- **`content` 格式**: 题目内容中的空格用 `_____` 表示。
- **示例**:
```json
{
  // ... 其他基本字段 ...
  "questionType": "填空题",
  "content": "混凝土结构是以混凝土为主要材料制成的结构, 包括 _____、_____和 _____等。其中,_____是目前土木工程中使用最为广泛的结构形式。",
  "blanks": [
    { "position": 1, "answer": "素混凝土结构" },
    { "position": 2, "answer": "钢筋混凝土结构" },
    { "position": 3, "answer": "预应力混凝土结构" },
    { "position": 4, "answer": "钢筋混凝土结构" }
  ],
  "analysis": "解析内容...",
  // ... 其他基本字段 ...
}
```

### 解答题 / 计算题 / 问答题

- **额外字段**:
    - `answer`: 包含详细解答步骤、计算过程或文字答案的字符串，可以包含换行符 (`\n`)。
- **示例**:
```json
{
  // ... 其他基本字段 ...
  "questionType": "问答题",
  "content": "什么是混凝土结构? 混凝土结构有哪些优点? 又有哪些缺点?",
  "answer": "答: 混凝土结构是以混凝土为主制成的结构, 包括素混凝土结构、钢筋混凝土结构和预应力混凝土结构等。\n其优点有: ① 就地取材; ② 合理用材、经济性好; ③ 耐久性好; ④ 耐火性好; ⑤ 可模性好; ⑥整体性好。\n其缺点有: ① 自重大; ② 抗裂性差; ③ 施工周期长等。",
  // ... 其他基本字段 ...
}
``` 