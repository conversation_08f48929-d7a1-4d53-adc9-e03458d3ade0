---
description: 
globs: 
alwaysApply: true
---
# 题型拆分规则

## 目标与原则

- **目标**: 将章节拆分后的Markdown文件按不同题型进一步拆分为独立文件
- **输入**: `章节拆分/`目录下的章节Markdown文件
- **输出**: 按题型组织的独立Markdown文件，保存在`题型拆分/[章节目录]/`下
- **内容完整性**: 必须保持原始内容完整，确保题目与对应答案/解析放在同一文件中
- **题型识别**: 准确识别各类题型标记，支持常见题型格式
- **目录结构**: 遵循章节-题型的两级目录结构组织

## 详细操作流程

1. **目录准备**:
   - 创建`题型拆分/`主目录
   - 为每个章节创建对应的子目录，命名与章节文件名（不含扩展名）相同
   - 例如：`题型拆分/第1章_绪论/`

2. **题型识别标准**:
   - 识别以下常见题型标记模式:
     - `## (一) 填空题` / `## 一、填空题` / `## 1. 填空题`
     - `### 单项选择题` / `### 二、单项选择题` / `### (二) 单项选择题`
     - `## 判断题` / `## 简答题` / `## 计算题` / `## 解答题` 等
   - 支持中文数字、阿拉伯数字、罗马数字和字母编号
   - 支持带括号和不带括号的编号格式
   - 支持二级、三级标题作为题型标记

3. **内容分割规则**:
   - 从一个题型标记开始，到下一个同级或更高级题型标记之间的所有内容视为该题型的完整内容
   - 包含题型标题本身和该题型下的所有题目及答案/解析
   - 处理题型嵌套情况，确保内容正确归属
   
4. **文件命名与保存**:
   - 文件命名使用题型的名称，例如：`填空题.md`、`单项选择题.md`、`判断题.md`
   - 移除题型名称中可能存在的编号（如"一、"、"(二)"等）
   - 将提取的完整题型内容保存到对应章节子目录下
   - 文件名去除特殊字符，必要时使用下划线替代空格或其他非法字符

5. **元数据添加**:
   - 可在文件开头添加YAML元数据，标记章节信息和题型信息:
     ```
     ---
     chapter: 1
     chapterName: 绪论
     questionType: 填空题
     count: 10  # 题目数量
     ---
     ```

## 特殊情况处理

1. **非标准题型结构**:
   - 对于不符合预期格式的题型标记，采用启发式规则进行识别
   - 可使用关键词匹配（如"填空"、"选择"、"判断"等）辅助识别

2. **混合题型处理**:
   - 若发现一个标题下包含多种题型（如"填空与选择题"），可根据实际情况:
     - 作为一个单独题型处理，或
     - 尝试进一步识别并分割为独立题型

3. **题型内子分类**:
   - 部分题型可能包含子分类（如"A组选择题"、"B组选择题"），应保持这些子分类的完整性
   - 可以子分类为单位进行拆分，或保持在同一题型文件中

4. **答案与解析处理**:
   - 识别题型末尾或文档末尾的答案/解析部分，确保与对应题目保持在同一文件中
   - 常见答案部分标记: `## 答案`、`### 参考答案`、`## 解析`等

5. **空题型处理**:
   - 如果识别到题型标记但没有实际内容，创建包含标题的空文件，以保持结构完整性

## 质量验证

拆分完成后，应执行以下验证:
- 确认所有题型均被正确识别并提取
- 验证每个题型文件包含完整的题目和对应答案/解析
- 检查特殊格式（公式、表格、图片等）是否正确保留
- 确认题型文件正确放置在对应的章节目录下